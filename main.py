import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
import warnings
from datetime import datetime, timedelta
import logging

# Настройка логирования
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Подавление предупреждений matplotlib
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
warnings.filterwarnings('ignore', category=FutureWarning)

# ============================================================================
# КОНФИГУРАЦИЯ И КОНСТАНТЫ
# ============================================================================

class Config:
    """Класс для хранения конфигурационных параметров."""

    # Параметры визуализации
    FIGURE_SIZE = (15, 8)
    TITLE_SIZE = 18
    LABEL_SIZE = 14
    TICK_SIZE = 11
    LEGEND_SIZE = 11

    # Цветовые схемы
    CITY_COLORS = [
        '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
        '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
    ]

    FUNNEL_COLORS = ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#16A085']
    CONVERSION_COLORS = ['#27AE60', '#E67E22', '#E74C3C', '#8E44AD']

    # Параметры алертов
    DEFAULT_OUTLIER_THRESHOLD = 2.5
    DEFAULT_DELAY_THRESHOLD_MIN = 60

    # Параметры отчетности
    REPORT_SECTIONS = [
        "📊 ОБЩАЯ СТАТИСТИКА",
        "🔄 АНАЛИЗ ВОРОНКИ КОНВЕРСИИ",
        "🏙️ СРАВНЕНИЕ ПО ГОРОДАМ",
        "⏰ АНАЛИЗ ОТЛОЖЕННЫХ ЗАКАЗОВ",
        "📈 ДЕТАЛЬНЫЕ МЕТРИКИ",
        "⚠️ АЛЕРТЫ И РЕКОМЕНДАЦИИ"
    ]

def set_plot_style() -> None:
    """
    Устанавливает единый стиль для всех графиков.

    Настраивает matplotlib для создания профессионально выглядящих графиков
    с оптимизированными размерами, шрифтами и цветами.
    """
    try:
        plt.style.use('seaborn-v0_8-whitegrid')
    except OSError:
        plt.style.use('default')
        logger.warning("Стиль seaborn недоступен, используется стандартный стиль")

    plt.rcParams.update({
        'figure.figsize': Config.FIGURE_SIZE,
        'axes.titlesize': Config.TITLE_SIZE,
        'axes.titleweight': 'bold',
        'axes.titlepad': 25,
        'axes.labelsize': Config.LABEL_SIZE,
        'xtick.labelsize': Config.TICK_SIZE,
        'ytick.labelsize': Config.TICK_SIZE,
        'legend.fontsize': Config.LEGEND_SIZE,
        'grid.alpha': 0.4,
        'lines.markersize': 8,
        'lines.linewidth': 2.8,
        'font.family': 'DejaVu Sans',
        'figure.autolayout': True
    })

# ============================================================================
# УТИЛИТЫ И ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ
# ============================================================================

def get_order_styles() -> Dict[str, Dict[str, Any]]:
    """
    Возвращает стили для различных типов заказов.

    Returns:
        Dict[str, Dict[str, Any]]: Словарь со стилями для срочных и отложенных заказов
    """
    return {
        'urgent': {
            'alpha': 0.9,
            'linestyle': '-',
            'linewidth': 3.0,
            'marker': 'o',
            'markersize': 9
        },
        'delayed': {
            'alpha': 0.7,
            'linestyle': '--',
            'linewidth': 2.5,
            'marker': 's',
            'markersize': 8
        }
    }

def validate_dataframe(df: pd.DataFrame, required_columns: List[str]) -> bool:
    """
    Проверяет наличие обязательных колонок в DataFrame.

    Args:
        df (pd.DataFrame): DataFrame для проверки
        required_columns (List[str]): Список обязательных колонок

    Returns:
        bool: True если все колонки присутствуют, False иначе
    """
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        logger.error(f"Отсутствуют обязательные колонки: {missing_columns}")
        return False
    return True

def safe_division(numerator: float, denominator: float, default: float = 0.0) -> float:
    """
    Безопасное деление с обработкой деления на ноль.

    Args:
        numerator (float): Числитель
        denominator (float): Знаменатель
        default (float): Значение по умолчанию при делении на ноль

    Returns:
        float: Результат деления или значение по умолчанию
    """
    return numerator / denominator if denominator != 0 else default

def format_number(value: float, decimal_places: int = 1) -> str:
    """
    Форматирует число для отображения.

    Args:
        value (float): Число для форматирования
        decimal_places (int): Количество знаков после запятой

    Returns:
        str: Отформатированная строка
    """
    if pd.isna(value):
        return "N/A"
    return f"{value:,.{decimal_places}f}"

# ============================================================================
# ЗАГРУЗКА И ПОДГОТОВКА ДАННЫХ
# ============================================================================

def load_and_prepare_data(filepath: str, date_format: str = None) -> pd.DataFrame:
    """
    Загружает и подготавливает данные из Excel файла.

    Args:
        filepath (str): Путь к Excel файлу
        date_format (str, optional): Формат даты для парсинга

    Returns:
        pd.DataFrame: Подготовленный DataFrame или пустой DataFrame при ошибке

    Raises:
        FileNotFoundError: Если файл не найден
        ValueError: Если данные имеют неправильный формат
    """
    required_columns = ['id_order', 'order_time', 'city']
    time_columns = ['trip_time', 'order_time', 'offer_time', 'assign_time', 'arrive_time']

    try:
        logger.info(f"Загрузка данных из файла: {filepath}")

        # Проверка существования файла
        import os
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"Файл не найден: {filepath}")

        # Загрузка данных
        df = pd.read_excel(filepath)
        logger.info(f"Загружено {len(df)} записей")

        # Проверка обязательных колонок
        if not validate_dataframe(df, required_columns):
            raise ValueError("Отсутствуют обязательные колонки в данных")

        # Преобразование временных колонок
        for col in time_columns:
            if col in df.columns:
                if date_format:
                    df[col] = pd.to_datetime(df[col], format=date_format, errors='coerce')
                else:
                    df[col] = pd.to_datetime(df[col], errors='coerce', dayfirst=True)

                # Логирование количества некорректных дат
                null_count = df[col].isnull().sum()
                if null_count > 0:
                    logger.warning(f"Колонка {col}: {null_count} некорректных дат")

        # Создание дополнительных колонок
        if 'order_time' in df.columns:
            df['day_order'] = df['order_time'].dt.day
            df['hour_order'] = df['order_time'].dt.hour
            df['weekday_order'] = df['order_time'].dt.dayofweek

        # Базовая валидация данных
        if len(df) == 0:
            raise ValueError("Загруженный файл не содержит данных")

        # Проверка на дубликаты
        duplicates = df.duplicated(subset=['id_order']).sum()
        if duplicates > 0:
            logger.warning(f"Найдено {duplicates} дублирующихся заказов")
            df = df.drop_duplicates(subset=['id_order'], keep='first')

        logger.info(f"Данные успешно подготовлены. Итоговое количество записей: {len(df)}")
        return df

    except FileNotFoundError as e:
        logger.error(f"Файл не найден: {e}")
        return pd.DataFrame()
    except ValueError as e:
        logger.error(f"Ошибка валидации данных: {e}")
        return pd.DataFrame()
    except Exception as e:
        logger.error(f"Неожиданная ошибка при загрузке данных: {e}")
        return pd.DataFrame()

# ============================================================================
# АЛГОРИТМЫ АНАЛИЗА ЗАКАЗОВ
# ============================================================================

def mark_delayed_orders(
    df: pd.DataFrame,
    threshold_min: int = None,
    criteria: Dict[str, Any] = None
) -> pd.DataFrame:
    """
    Определяет отложенные заказы на основе различных критериев.

    Args:
        df (pd.DataFrame): DataFrame с данными заказов
        threshold_min (int, optional): Порог в минутах для определения отложенного заказа
        criteria (Dict[str, Any], optional): Дополнительные критерии для классификации

    Returns:
        pd.DataFrame: DataFrame с добавленными колонками анализа задержек

    Raises:
        ValueError: Если отсутствуют необходимые колонки
    """
    if threshold_min is None:
        threshold_min = Config.DEFAULT_DELAY_THRESHOLD_MIN

    if criteria is None:
        criteria = {}

    # Создаем копию для безопасности
    df = df.copy()

    # Проверяем наличие необходимых колонок
    required_cols = ['order_time']
    if not validate_dataframe(df, required_cols):
        raise ValueError("Отсутствуют необходимые колонки для анализа задержек")

    # Инициализируем колонки по умолчанию
    df['is_delayed'] = False
    df['delay_min'] = 0.0
    df['delay_category'] = 'unknown'

    try:
        # Основной алгоритм: задержка между заказом и назначением
        if 'assign_time' in df.columns:
            # Вычисляем задержку в минутах
            time_diff = df['assign_time'] - df['order_time']
            df['delay_min'] = time_diff.dt.total_seconds() / 60

            # Обрабатываем некорректные значения
            df['delay_min'] = df['delay_min'].fillna(0)
            df['delay_min'] = df['delay_min'].clip(lower=0)  # Убираем отрицательные значения

            # Основная классификация по времени
            df['is_delayed'] = df['delay_min'] > threshold_min

            # Детальная категоризация задержек
            conditions = [
                df['delay_min'] <= 5,
                (df['delay_min'] > 5) & (df['delay_min'] <= 15),
                (df['delay_min'] > 15) & (df['delay_min'] <= threshold_min),
                (df['delay_min'] > threshold_min) & (df['delay_min'] <= 120),
                df['delay_min'] > 120
            ]

            categories = [
                'мгновенный',
                'быстрый',
                'нормальный',
                'отложенный',
                'критически_отложенный'
            ]

            df['delay_category'] = np.select(conditions, categories, default='unknown')

        # Дополнительные критерии из параметра criteria
        if criteria.get('use_offer_time', False) and 'offer_time' in df.columns:
            # Альтернативный критерий: задержка до предложения
            offer_delay = (df['offer_time'] - df['order_time']).dt.total_seconds() / 60
            offer_threshold = criteria.get('offer_threshold_min', 10)
            df['is_delayed'] = df['is_delayed'] | (offer_delay > offer_threshold)

        if criteria.get('use_time_of_day', False):
            # Учитываем время суток (ночные заказы могут быть отложенными по умолчанию)
            night_hours = criteria.get('night_hours', [0, 1, 2, 3, 4, 5])
            if 'hour_order' in df.columns:
                is_night = df['hour_order'].isin(night_hours)
                night_threshold = criteria.get('night_threshold_min', threshold_min // 2)
                df.loc[is_night, 'is_delayed'] = df.loc[is_night, 'delay_min'] > night_threshold

        # Статистика по результатам
        total_orders = len(df)
        delayed_orders = df['is_delayed'].sum()
        delayed_percentage = (delayed_orders / total_orders * 100) if total_orders > 0 else 0

        logger.info(f"Анализ задержек завершен:")
        logger.info(f"  - Всего заказов: {total_orders}")
        logger.info(f"  - Отложенных заказов: {delayed_orders} ({delayed_percentage:.1f}%)")
        logger.info(f"  - Порог задержки: {threshold_min} минут")

        # Статистика по категориям
        if 'delay_category' in df.columns:
            category_stats = df['delay_category'].value_counts()
            logger.info("  - Распределение по категориям:")
            for category, count in category_stats.items():
                percentage = (count / total_orders * 100) if total_orders > 0 else 0
                logger.info(f"    {category}: {count} ({percentage:.1f}%)")

        return df

    except Exception as e:
        logger.error(f"Ошибка при анализе задержек: {e}")
        # Возвращаем DataFrame с базовыми значениями при ошибке
        df['is_delayed'] = False
        df['delay_min'] = 0.0
        df['delay_category'] = 'error'
        return df

def aggregate_metrics(
    df: pd.DataFrame,
    segment_col: Optional[str] = None,
    group_by: List[str] = None
) -> pd.DataFrame:
    """
    Агрегирует метрики и рассчитывает конверсии для анализа воронки.

    Args:
        df (pd.DataFrame): DataFrame с данными заказов
        segment_col (str, optional): Колонка для сегментации (например, 'is_delayed')
        group_by (List[str], optional): Дополнительные колонки для группировки

    Returns:
        pd.DataFrame: Агрегированные данные с рассчитанными конверсиями

    Raises:
        ValueError: Если отсутствуют необходимые колонки
    """
    # Определяем колонки для группировки
    if group_by is None:
        group_cols = ['day_order', 'city']
    else:
        group_cols = group_by.copy()

    if segment_col and segment_col in df.columns:
        group_cols.append(segment_col)

    # Проверяем наличие необходимых колонок
    required_cols = ['id_order'] + [col for col in group_cols if col in df.columns]
    if not validate_dataframe(df, required_cols):
        raise ValueError("Отсутствуют необходимые колонки для агрегации")

    try:
        logger.info(f"Агрегация данных по колонкам: {group_cols}")

        # Создаем результирующий DataFrame
        agg_results = []

        # Группируем данные
        grouped = df.groupby(group_cols)

        for group_name, group_data in grouped:
            # Создаем словарь для текущей группы
            result_row = {}

            # Добавляем значения группировочных колонок
            if len(group_cols) == 1:
                result_row[group_cols[0]] = group_name
            else:
                for i, col in enumerate(group_cols):
                    result_row[col] = group_name[i]

            # Основные метрики
            result_row['cnt_order'] = len(group_data)
            result_row['cnt_offer'] = group_data['offer_time'].notna().sum()
            result_row['cnt_assign'] = group_data['assign_time'].notna().sum()
            result_row['cnt_arrive'] = group_data['arrive_time'].notna().sum()
            result_row['cnt_trip'] = group_data['trip_time'].notna().sum()

            # Дополнительные метрики если есть соответствующие колонки
            if 'delay_min' in group_data.columns:
                delay_data = group_data['delay_min'].dropna()
                if len(delay_data) > 0:
                    result_row['avg_delay_min'] = delay_data.mean()
                    result_row['median_delay_min'] = delay_data.median()
                    result_row['max_delay_min'] = delay_data.max()
                else:
                    result_row['avg_delay_min'] = 0
                    result_row['median_delay_min'] = 0
                    result_row['max_delay_min'] = 0

            if 'flag_trip' in group_data.columns:
                result_row['cnt_successful_trip'] = group_data['flag_trip'].sum()

            agg_results.append(result_row)

        # Создаем DataFrame из результатов
        agg = pd.DataFrame(agg_results)

        # Рассчитываем конверсии с безопасным делением
        conversion_metrics = [
            ('cnt_trip', 'cnt_order', 'order2trip'),
            ('cnt_offer', 'cnt_order', 'order2offer'),
            ('cnt_assign', 'cnt_offer', 'offer2assign'),
            ('cnt_arrive', 'cnt_assign', 'assign2arrive'),
            ('cnt_trip', 'cnt_arrive', 'arrive2trip')
        ]

        for numerator, denominator, metric_name in conversion_metrics:
            if numerator in agg.columns and denominator in agg.columns:
                agg[metric_name] = agg.apply(
                    lambda row: safe_division(row[numerator], row[denominator]),
                    axis=1
                )

        # Дополнительные метрики
        if 'cnt_successful_trip' in agg.columns and 'cnt_trip' in agg.columns:
            agg['trip_success_rate'] = agg.apply(
                lambda row: safe_division(row['cnt_successful_trip'], row['cnt_trip']),
                axis=1
            )

        # Логирование результатов
        total_groups = len(agg)
        logger.info(f"Создано {total_groups} групп для анализа")

        if total_groups > 0:
            # Статистика по основным метрикам
            for metric in ['order2trip', 'order2offer', 'offer2assign']:
                if metric in agg.columns:
                    avg_conversion = agg[metric].mean() * 100
                    logger.info(f"Средняя конверсия {metric}: {avg_conversion:.1f}%")

        return agg

    except Exception as e:
        logger.error(f"Ошибка при агрегации метрик: {e}")
        # Возвращаем пустой DataFrame с правильными колонками при ошибке
        return pd.DataFrame(columns=group_cols + ['cnt_order', 'order2trip'])

# ============================================================================
# СИСТЕМА АЛЕРТОВ И МОНИТОРИНГА
# ============================================================================

class AlertSystem:
    """Система алертов для мониторинга аномалий в данных."""

    def __init__(self, threshold: float = None):
        """
        Инициализация системы алертов.

        Args:
            threshold (float): Порог для определения выбросов
        """
        self.threshold = threshold or Config.DEFAULT_OUTLIER_THRESHOLD
        self.alerts = []

    def add_alert(self, alert_type: str, message: str, severity: str = "WARNING", data: Dict = None):
        """
        Добавляет новый алерт в систему.

        Args:
            alert_type (str): Тип алерта
            message (str): Сообщение алерта
            severity (str): Уровень важности (INFO, WARNING, ERROR, CRITICAL)
            data (Dict): Дополнительные данные
        """
        alert = {
            'timestamp': datetime.now(),
            'type': alert_type,
            'severity': severity,
            'message': message,
            'data': data or {}
        }
        self.alerts.append(alert)

        # Логирование в зависимости от уровня
        if severity == "CRITICAL":
            logger.critical(f"🚨 {alert_type}: {message}")
        elif severity == "ERROR":
            logger.error(f"❌ {alert_type}: {message}")
        elif severity == "WARNING":
            logger.warning(f"⚠️ {alert_type}: {message}")
        else:
            logger.info(f"ℹ️ {alert_type}: {message}")

    def check_outliers(self, df: pd.DataFrame, metric_cols: List[str]) -> None:
        """
        Проверяет наличие выбросов в указанных метриках.

        Args:
            df (pd.DataFrame): DataFrame для анализа
            metric_cols (List[str]): Список колонок для проверки
        """
        for col in metric_cols:
            if col not in df.columns:
                continue

            # Убираем NaN значения
            clean_data = df[col].dropna()
            if len(clean_data) == 0:
                continue

            # Используем метод MAD (Median Absolute Deviation)
            median = clean_data.median()
            mad = np.median(np.abs(clean_data - median))

            if mad == 0:
                # Если MAD = 0, используем стандартное отклонение
                std = clean_data.std()
                if std > 0:
                    outliers = df[np.abs(df[col] - median) > self.threshold * std]
                else:
                    continue
            else:
                outliers = df[np.abs(df[col] - median) > self.threshold * mad]

            # Создаем алерты для каждого выброса
            for _, row in outliers.iterrows():
                value = row[col]
                city = row.get('city', 'Unknown')
                day = row.get('day_order', 'Unknown')

                message = f"Выброс в метрике '{col}': {value:.2f} (город: {city}, день: {day})"

                severity = "WARNING"
                if abs(value - median) > 3 * (mad if mad > 0 else clean_data.std()):
                    severity = "ERROR"

                self.add_alert(
                    alert_type="OUTLIER",
                    message=message,
                    severity=severity,
                    data={
                        'metric': col,
                        'value': value,
                        'median': median,
                        'city': city,
                        'day': day,
                        'deviation': abs(value - median)
                    }
                )

    def check_conversion_drops(self, df: pd.DataFrame, min_conversion: float = 0.1) -> None:
        """
        Проверяет критические падения конверсии.

        Args:
            df (pd.DataFrame): DataFrame с метриками
            min_conversion (float): Минимальный приемлемый уровень конверсии
        """
        conversion_metrics = ['order2trip', 'order2offer', 'offer2assign', 'assign2arrive', 'arrive2trip']

        for metric in conversion_metrics:
            if metric not in df.columns:
                continue

            low_conversion = df[df[metric] < min_conversion]

            for _, row in low_conversion.iterrows():
                city = row.get('city', 'Unknown')
                day = row.get('day_order', 'Unknown')
                value = row[metric]

                severity = "ERROR" if value < min_conversion / 2 else "WARNING"

                self.add_alert(
                    alert_type="LOW_CONVERSION",
                    message=f"Низкая конверсия {metric}: {value:.1%} (город: {city}, день: {day})",
                    severity=severity,
                    data={
                        'metric': metric,
                        'value': value,
                        'city': city,
                        'day': day,
                        'threshold': min_conversion
                    }
                )

    def check_data_quality(self, df: pd.DataFrame) -> None:
        """
        Проверяет качество данных.

        Args:
            df (pd.DataFrame): DataFrame для проверки
        """
        # Проверка на пустые значения
        for col in df.columns:
            null_count = df[col].isnull().sum()
            null_percentage = (null_count / len(df)) * 100

            if null_percentage > 50:
                self.add_alert(
                    alert_type="DATA_QUALITY",
                    message=f"Высокий процент пустых значений в колонке '{col}': {null_percentage:.1f}%",
                    severity="ERROR",
                    data={'column': col, 'null_percentage': null_percentage}
                )
            elif null_percentage > 20:
                self.add_alert(
                    alert_type="DATA_QUALITY",
                    message=f"Значительный процент пустых значений в колонке '{col}': {null_percentage:.1f}%",
                    severity="WARNING",
                    data={'column': col, 'null_percentage': null_percentage}
                )

        # Проверка на аномальные временные интервалы
        if 'delay_min' in df.columns:
            extreme_delays = df[df['delay_min'] > 1440]  # Более 24 часов
            if len(extreme_delays) > 0:
                self.add_alert(
                    alert_type="DATA_QUALITY",
                    message=f"Найдено {len(extreme_delays)} заказов с задержкой более 24 часов",
                    severity="WARNING",
                    data={'extreme_delays_count': len(extreme_delays)}
                )

    def generate_report(self) -> str:
        """
        Генерирует отчет по всем алертам.

        Returns:
            str: Форматированный отчет
        """
        if not self.alerts:
            return "✅ Алертов не обнаружено. Все показатели в норме."

        # Группируем алерты по типам и уровням важности
        by_severity = {}
        by_type = {}

        for alert in self.alerts:
            severity = alert['severity']
            alert_type = alert['type']

            if severity not in by_severity:
                by_severity[severity] = []
            by_severity[severity].append(alert)

            if alert_type not in by_type:
                by_type[alert_type] = []
            by_type[alert_type].append(alert)

        report = []
        report.append("⚠️ ОТЧЕТ ПО АЛЕРТАМ")
        report.append("=" * 50)

        # Сводка по уровням важности
        report.append("\n📊 СВОДКА ПО УРОВНЯМ ВАЖНОСТИ:")
        for severity in ['CRITICAL', 'ERROR', 'WARNING', 'INFO']:
            if severity in by_severity:
                count = len(by_severity[severity])
                emoji = {'CRITICAL': '🚨', 'ERROR': '❌', 'WARNING': '⚠️', 'INFO': 'ℹ️'}[severity]
                report.append(f"  {emoji} {severity}: {count}")

        # Детальные алерты по типам
        report.append("\n📋 ДЕТАЛЬНАЯ ИНФОРМАЦИЯ:")
        for alert_type, alerts in by_type.items():
            report.append(f"\n🔍 {alert_type} ({len(alerts)} алертов):")
            for alert in alerts[:5]:  # Показываем только первые 5 алертов каждого типа
                timestamp = alert['timestamp'].strftime('%H:%M:%S')
                severity_emoji = {'CRITICAL': '🚨', 'ERROR': '❌', 'WARNING': '⚠️', 'INFO': 'ℹ️'}[alert['severity']]
                report.append(f"  {severity_emoji} [{timestamp}] {alert['message']}")

            if len(alerts) > 5:
                report.append(f"  ... и еще {len(alerts) - 5} алертов")

        return "\n".join(report)

    def clear_alerts(self) -> None:
        """Очищает все алерты."""
        self.alerts.clear()

def alert_outliers(df: pd.DataFrame, metric_cols: List[str], threshold: float = 2.5) -> AlertSystem:
    """
    Совместимая функция для проверки выбросов (для обратной совместимости).

    Args:
        df (pd.DataFrame): DataFrame для анализа
        metric_cols (List[str]): Список метрик для проверки
        threshold (float): Порог для определения выбросов

    Returns:
        AlertSystem: Система алертов с результатами проверки
    """
    alert_system = AlertSystem(threshold)
    alert_system.check_outliers(df, metric_cols)
    alert_system.check_conversion_drops(df)
    alert_system.check_data_quality(df)

    # Выводим отчет для совместимости
    report = alert_system.generate_report()
    print(f"\n{report}")

    return alert_system

# ============================================================================
# СИСТЕМА ОТЧЕТНОСТИ
# ============================================================================

class ReportGenerator:
    """Генератор структурированных отчетов для анализа данных такси."""

    def __init__(self):
        """Инициализация генератора отчетов."""
        self.sections = []
        self.key_insights = []
        self.recommendations = []

    def add_section(self, title: str, content: str, data: Dict = None) -> None:
        """
        Добавляет секцию в отчет.

        Args:
            title (str): Заголовок секции
            content (str): Содержимое секции
            data (Dict): Дополнительные данные для секции
        """
        section = {
            'title': title,
            'content': content,
            'data': data or {},
            'timestamp': datetime.now()
        }
        self.sections.append(section)

    def add_insight(self, insight: str, importance: str = "MEDIUM") -> None:
        """
        Добавляет ключевой вывод.

        Args:
            insight (str): Текст вывода
            importance (str): Важность (LOW, MEDIUM, HIGH, CRITICAL)
        """
        self.key_insights.append({
            'text': insight,
            'importance': importance,
            'timestamp': datetime.now()
        })

    def add_recommendation(self, recommendation: str, priority: str = "MEDIUM") -> None:
        """
        Добавляет рекомендацию.

        Args:
            recommendation (str): Текст рекомендации
            priority (str): Приоритет (LOW, MEDIUM, HIGH, URGENT)
        """
        self.recommendations.append({
            'text': recommendation,
            'priority': priority,
            'timestamp': datetime.now()
        })

    def analyze_general_stats(self, df: pd.DataFrame) -> None:
        """
        Анализирует общую статистику и добавляет в отчет.

        Args:
            df (pd.DataFrame): Исходные данные
        """
        if df.empty:
            self.add_section("📊 ОБЩАЯ СТАТИСТИКА", "Данные отсутствуют")
            return

        total_orders = len(df)
        cities = df['city'].nunique() if 'city' in df.columns else 0
        date_range = ""

        if 'order_time' in df.columns:
            min_date = df['order_time'].min()
            max_date = df['order_time'].max()
            if pd.notna(min_date) and pd.notna(max_date):
                date_range = f"с {min_date.strftime('%d.%m.%Y')} по {max_date.strftime('%d.%m.%Y')}"

        # Статистика по завершенным поездкам
        completed_trips = 0
        if 'trip_time' in df.columns:
            completed_trips = df['trip_time'].notna().sum()

        # Статистика по задержкам
        delayed_info = ""
        if 'is_delayed' in df.columns:
            delayed_count = df['is_delayed'].sum()
            delayed_percentage = (delayed_count / total_orders * 100) if total_orders > 0 else 0
            delayed_info = f"\n• Отложенных заказов: {delayed_count:,} ({delayed_percentage:.1f}%)"

        content = f"""
📈 Основные показатели:
• Всего заказов: {total_orders:,}
• Количество городов: {cities}
• Период анализа: {date_range}
• Завершенных поездок: {completed_trips:,}{delayed_info}

🎯 Общая конверсия заказов в поездки: {(completed_trips/total_orders*100):.1f}%
        """.strip()

        self.add_section("📊 ОБЩАЯ СТАТИСТИКА", content, {
            'total_orders': total_orders,
            'cities_count': cities,
            'completed_trips': completed_trips,
            'overall_conversion': completed_trips/total_orders if total_orders > 0 else 0
        })

        # Добавляем ключевые выводы
        if total_orders > 1000:
            self.add_insight(f"Большой объем данных ({total_orders:,} заказов) обеспечивает статистическую значимость анализа", "HIGH")

        overall_conversion = completed_trips/total_orders if total_orders > 0 else 0
        if overall_conversion < 0.5:
            self.add_insight(f"Низкая общая конверсия ({overall_conversion:.1%}) требует детального анализа воронки", "CRITICAL")
            self.add_recommendation("Провести анализ причин низкой конверсии на каждом этапе воронки", "URGENT")
        elif overall_conversion > 0.8:
            self.add_insight(f"Высокая общая конверсия ({overall_conversion:.1%}) свидетельствует об эффективной работе сервиса", "HIGH")

    def analyze_funnel(self, agg_df: pd.DataFrame) -> None:
        """
        Анализирует воронку конверсии.

        Args:
            agg_df (pd.DataFrame): Агрегированные данные
        """
        if agg_df.empty:
            self.add_section("🔄 АНАЛИЗ ВОРОНКИ КОНВЕРСИИ", "Данные для анализа воронки отсутствуют")
            return

        # Суммируем данные по всем городам и дням
        total_data = agg_df.groupby(lambda x: True).agg({
            'cnt_order': 'sum',
            'cnt_offer': 'sum',
            'cnt_assign': 'sum',
            'cnt_arrive': 'sum',
            'cnt_trip': 'sum'
        }).iloc[0] if len(agg_df) > 0 else pd.Series()

        if total_data.empty:
            return

        # Рассчитываем конверсии
        conversions = {}
        stages = [
            ('cnt_order', 'cnt_offer', 'order2offer', 'Заказ → Предложение'),
            ('cnt_offer', 'cnt_assign', 'offer2assign', 'Предложение → Назначение'),
            ('cnt_assign', 'cnt_arrive', 'assign2arrive', 'Назначение → Прибытие'),
            ('cnt_arrive', 'cnt_trip', 'arrive2trip', 'Прибытие → Поездка'),
            ('cnt_order', 'cnt_trip', 'order2trip', 'Заказ → Поездка (общая)')
        ]

        content_lines = ["🔄 Анализ этапов воронки конверсии:\n"]

        for from_col, to_col, conv_name, description in stages:
            if from_col in total_data.index and to_col in total_data.index:
                from_val = total_data[from_col]
                to_val = total_data[to_col]
                conversion = safe_division(to_val, from_val)
                conversions[conv_name] = conversion

                # Определяем статус конверсии
                if conversion >= 0.8:
                    status = "🟢 Отлично"
                elif conversion >= 0.6:
                    status = "🟡 Хорошо"
                elif conversion >= 0.4:
                    status = "🟠 Требует внимания"
                else:
                    status = "🔴 Критично"

                content_lines.append(f"• {description}: {conversion:.1%} {status}")

                # Добавляем выводы и рекомендации
                if conversion < 0.3:
                    self.add_insight(f"Критически низкая конверсия на этапе '{description}': {conversion:.1%}", "CRITICAL")
                    self.add_recommendation(f"Срочно проанализировать причины низкой конверсии на этапе '{description}'", "URGENT")
                elif conversion < 0.5:
                    self.add_insight(f"Низкая конверсия на этапе '{description}': {conversion:.1%}", "HIGH")
                    self.add_recommendation(f"Оптимизировать процесс на этапе '{description}'", "HIGH")

        # Находим самое слабое звено
        if conversions:
            min_conversion = min(conversions.items(), key=lambda x: x[1])
            content_lines.append(f"\n🎯 Самое слабое звено: {min_conversion[0]} ({min_conversion[1]:.1%})")

            if min_conversion[1] < 0.5:
                self.add_recommendation(f"Приоритетно работать над улучшением этапа {min_conversion[0]}", "HIGH")

        self.add_section("🔄 АНАЛИЗ ВОРОНКИ КОНВЕРСИИ", "\n".join(content_lines), conversions)

    def analyze_cities(self, agg_df: pd.DataFrame) -> None:
        """
        Анализирует показатели по городам.

        Args:
            agg_df (pd.DataFrame): Агрегированные данные
        """
        if agg_df.empty or 'city' not in agg_df.columns:
            self.add_section("🏙️ СРАВНЕНИЕ ПО ГОРОДАМ", "Данные по городам отсутствуют")
            return

        # Агрегируем по городам
        city_stats = agg_df.groupby('city').agg({
            'cnt_order': 'sum',
            'cnt_trip': 'sum'
        }).reset_index()

        city_stats['conversion'] = city_stats.apply(
            lambda row: safe_division(row['cnt_trip'], row['cnt_order']), axis=1
        )

        city_stats = city_stats.sort_values('conversion', ascending=False)

        content_lines = ["🏙️ Показатели по городам:\n"]

        for _, row in city_stats.iterrows():
            city = row['city']
            orders = int(row['cnt_order'])
            conversion = row['conversion']

            # Определяем рейтинг города
            if conversion >= 0.7:
                rating = "⭐⭐⭐"
            elif conversion >= 0.5:
                rating = "⭐⭐"
            elif conversion >= 0.3:
                rating = "⭐"
            else:
                rating = "❌"

            content_lines.append(f"• {city}: {conversion:.1%} ({orders:,} заказов) {rating}")

        # Находим лучший и худший город
        if len(city_stats) > 1:
            best_city = city_stats.iloc[0]
            worst_city = city_stats.iloc[-1]

            content_lines.append(f"\n🏆 Лучший город: {best_city['city']} ({best_city['conversion']:.1%})")
            content_lines.append(f"📉 Требует внимания: {worst_city['city']} ({worst_city['conversion']:.1%})")

            # Добавляем выводы
            if best_city['conversion'] - worst_city['conversion'] > 0.3:
                self.add_insight(f"Большой разрыв в конверсии между городами: {best_city['conversion']:.1%} vs {worst_city['conversion']:.1%}", "HIGH")
                self.add_recommendation(f"Изучить лучшие практики города {best_city['city']} для применения в {worst_city['city']}", "MEDIUM")

        self.add_section("🏙️ СРАВНЕНИЕ ПО ГОРОДАМ", "\n".join(content_lines), city_stats.to_dict('records'))

    def generate_executive_summary(self) -> str:
        """
        Генерирует краткое резюме для руководства.

        Returns:
            str: Исполнительное резюме
        """
        summary_lines = []
        summary_lines.append("📋 ИСПОЛНИТЕЛЬНОЕ РЕЗЮМЕ")
        summary_lines.append("=" * 50)

        # Ключевые выводы по важности
        if self.key_insights:
            summary_lines.append("\n🎯 КЛЮЧЕВЫЕ ВЫВОДЫ:")

            # Сортируем по важности
            importance_order = {'CRITICAL': 0, 'HIGH': 1, 'MEDIUM': 2, 'LOW': 3}
            sorted_insights = sorted(self.key_insights, key=lambda x: importance_order.get(x['importance'], 4))

            for insight in sorted_insights[:5]:  # Топ-5 выводов
                importance_emoji = {'CRITICAL': '🚨', 'HIGH': '🔴', 'MEDIUM': '🟡', 'LOW': '🟢'}
                emoji = importance_emoji.get(insight['importance'], '📌')
                summary_lines.append(f"  {emoji} {insight['text']}")

        # Приоритетные рекомендации
        if self.recommendations:
            summary_lines.append("\n💡 ПРИОРИТЕТНЫЕ РЕКОМЕНДАЦИИ:")

            # Сортируем по приоритету
            priority_order = {'URGENT': 0, 'HIGH': 1, 'MEDIUM': 2, 'LOW': 3}
            sorted_recommendations = sorted(self.recommendations, key=lambda x: priority_order.get(x['priority'], 4))

            for i, rec in enumerate(sorted_recommendations[:3], 1):  # Топ-3 рекомендации
                priority_emoji = {'URGENT': '🚨', 'HIGH': '🔴', 'MEDIUM': '🟡', 'LOW': '🟢'}
                emoji = priority_emoji.get(rec['priority'], '📌')
                summary_lines.append(f"  {i}. {emoji} {rec['text']}")

        return "\n".join(summary_lines)

    def generate_full_report(self) -> str:
        """
        Генерирует полный отчет.

        Returns:
            str: Полный отчет
        """
        report_lines = []

        # Заголовок отчета
        timestamp = datetime.now().strftime('%d.%m.%Y %H:%M')
        report_lines.append("📊 ОТЧЕТ ПО АНАЛИЗУ ДАННЫХ ТАКСИ")
        report_lines.append("=" * 60)
        report_lines.append(f"Дата создания: {timestamp}")
        report_lines.append("")

        # Исполнительное резюме
        report_lines.append(self.generate_executive_summary())
        report_lines.append("")

        # Детальные секции
        report_lines.append("📖 ДЕТАЛЬНЫЙ АНАЛИЗ")
        report_lines.append("=" * 40)

        for section in self.sections:
            report_lines.append(f"\n{section['title']}")
            report_lines.append("-" * len(section['title']))
            report_lines.append(section['content'])

        return "\n".join(report_lines)

# ============================================================================
# УЛУЧШЕННЫЕ ФУНКЦИИ ВИЗУАЛИЗАЦИИ
# ============================================================================

def setup_plot_aesthetics(ax: plt.Axes, title: str, xlabel: str = None, ylabel: str = None) -> None:
    """
    Настраивает эстетику графика с профессиональным оформлением.

    Args:
        ax (plt.Axes): Объект осей matplotlib
        title (str): Заголовок графика
        xlabel (str, optional): Подпись оси X
        ylabel (str, optional): Подпись оси Y
    """
    # Заголовок
    ax.set_title(title, fontsize=Config.TITLE_SIZE, fontweight='bold', pad=25)

    # Подписи осей
    if xlabel:
        ax.set_xlabel(xlabel, fontsize=Config.LABEL_SIZE, fontweight='bold')
    if ylabel:
        ax.set_ylabel(ylabel, fontsize=Config.LABEL_SIZE, fontweight='bold')

    # Улучшенная сетка
    ax.grid(True, linestyle='-', alpha=0.3, linewidth=0.8, which='major')
    ax.grid(True, linestyle=':', alpha=0.2, linewidth=0.5, which='minor')
    ax.set_axisbelow(True)

    # Стиль рамки
    for spine in ax.spines.values():
        spine.set_linewidth(1.2)
        spine.set_color('#333333')

def add_value_labels(ax: plt.Axes, bars, values: List[float], format_str: str = "{:.1f}%") -> None:
    """
    Добавляет подписи значений на столбчатые диаграммы.

    Args:
        ax (plt.Axes): Объект осей matplotlib
        bars: Объекты столбцов matplotlib
        values (List[float]): Значения для подписей
        format_str (str): Формат строки для значений
    """
    max_height = max([bar.get_height() for bar in bars]) if bars else 0

    for bar, value in zip(bars, values):
        if pd.isna(value) or value == 0:
            continue

        height = bar.get_height()
        label_text = format_str.format(value)

        # Определяем позицию подписи
        y_offset = max_height * 0.02
        y_pos = height + y_offset

        # Добавляем подпись с улучшенным оформлением
        ax.text(bar.get_x() + bar.get_width()/2., y_pos,
                label_text, ha='center', va='bottom',
                fontweight='bold', fontsize=10,
                bbox=dict(boxstyle='round,pad=0.3',
                         facecolor='white',
                         alpha=0.9,
                         edgecolor='gray',
                         linewidth=0.8))

def create_enhanced_legend(ax: plt.Axes, title: str = None, location: str = 'best') -> None:
    """
    Создает улучшенную легенду для графика.

    Args:
        ax (plt.Axes): Объект осей matplotlib
        title (str, optional): Заголовок легенды
        location (str): Расположение легенды
    """
    legend = ax.legend(
        title=title,
        loc=location,
        frameon=True,
        fancybox=True,
        shadow=True,
        title_fontsize=12,
        fontsize=Config.LEGEND_SIZE,
        borderaxespad=1.0
    )

    # Настройка рамки легенды
    legend.get_frame().set_facecolor('white')
    legend.get_frame().set_alpha(0.95)
    legend.get_frame().set_edgecolor('gray')
    legend.get_frame().set_linewidth(1.0)

    # Настройка заголовка легенды
    if title:
        legend.get_title().set_fontweight('bold')

def add_trend_line(ax: plt.Axes, x_data: List, y_data: List, color: str = 'red') -> None:
    """
    Добавляет линию тренда на график.

    Args:
        ax (plt.Axes): Объект осей matplotlib
        x_data (List): Данные по оси X
        y_data (List): Данные по оси Y
        color (str): Цвет линии тренда
    """
    try:
        # Убираем NaN значения
        clean_data = [(x, y) for x, y in zip(x_data, y_data) if not pd.isna(y)]
        if len(clean_data) < 2:
            return

        x_clean, y_clean = zip(*clean_data)

        # Вычисляем линейный тренд
        z = np.polyfit(x_clean, y_clean, 1)
        p = np.poly1d(z)

        # Добавляем линию тренда
        ax.plot(x_clean, p(x_clean),
                color=color, linestyle='--', linewidth=2, alpha=0.7,
                label=f'Тренд (наклон: {z[0]:.3f})')

    except Exception as e:
        logger.warning(f"Не удалось добавить линию тренда: {e}")

def save_plot_if_needed(fig: plt.Figure, filename: str = None, dpi: int = 300) -> None:
    """
    Сохраняет график в файл при необходимости.

    Args:
        fig (plt.Figure): Объект фигуры matplotlib
        filename (str, optional): Имя файла для сохранения
        dpi (int): Разрешение для сохранения
    """
    if filename:
        try:
            fig.savefig(filename, dpi=dpi, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            logger.info(f"График сохранен: {filename}")
        except Exception as e:
            logger.error(f"Ошибка при сохранении графика: {e}")

def create_color_palette(n_colors: int, palette_type: str = 'city') -> List[str]:
    """
    Создает цветовую палитру для графиков.

    Args:
        n_colors (int): Количество необходимых цветов
        palette_type (str): Тип палитры ('city', 'funnel', 'conversion')

    Returns:
        List[str]: Список цветов в hex формате
    """
    if palette_type == 'city':
        base_colors = Config.CITY_COLORS
    elif palette_type == 'funnel':
        base_colors = Config.FUNNEL_COLORS
    elif palette_type == 'conversion':
        base_colors = Config.CONVERSION_COLORS
    else:
        base_colors = Config.CITY_COLORS

    # Если нужно больше цветов, чем есть в базовой палитре, генерируем дополнительные
    if n_colors <= len(base_colors):
        return base_colors[:n_colors]
    else:
        # Используем matplotlib для генерации дополнительных цветов
        import matplotlib.cm as cm
        import matplotlib.colors as mcolors

        additional_colors = []
        colormap = cm.get_cmap('tab20')
        for i in range(len(base_colors), n_colors):
            color = colormap(i / n_colors)
            additional_colors.append(mcolors.to_hex(color))

        return base_colors + additional_colors

def plot_metric(
    df: pd.DataFrame,
    metric_col: str,
    title: str,
    ylim: tuple = (0, 100),
    cities: List[str] = None,
    segment_col: Optional[str] = None,
    show_trend: bool = True,
    save_filename: str = None
) -> plt.Figure:
    """
    Создает улучшенный столбчатый график метрики по городам для лучшей читаемости.

    Args:
        df (pd.DataFrame): DataFrame с данными
        metric_col (str): Название колонки с метрикой
        title (str): Заголовок графика
        ylim (tuple): Пределы оси Y
        cities (List[str], optional): Список городов для отображения
        segment_col (str, optional): Колонка для сегментации
        show_trend (bool): Показывать ли линию тренда
        save_filename (str, optional): Имя файла для сохранения

    Returns:
        plt.Figure: Объект фигуры matplotlib
    """
    # Валидация данных
    if df.empty:
        logger.warning(f"Пустой DataFrame для метрики {metric_col}")
        return plt.figure()

    if metric_col not in df.columns:
        logger.error(f"Колонка {metric_col} не найдена в данных")
        return plt.figure()

    set_plot_style()

    if cities is None:
        cities = df['city'].unique() if 'city' in df.columns else []

    # Ограничиваем количество городов для лучшей читаемости
    if len(cities) > 6:
        # Берем топ-6 городов по количеству заказов
        city_orders = df.groupby('city')['cnt_order'].sum().sort_values(ascending=False)
        cities = city_orders.head(6).index.tolist()
        logger.info(f"Ограничено до топ-6 городов для лучшей читаемости: {cities}")

    # Создаем цветовую палитру
    colors = create_color_palette(len(cities), 'city')

    try:
        # Используем subplot для лучшего контроля
        if segment_col and segment_col in df.columns:
            # Создаем два подграфика для сравнения типов заказов
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))

            # График для срочных заказов
            urgent_data = df[df[segment_col] == False] if segment_col in df.columns else df
            plot_city_bars(ax1, urgent_data, metric_col, cities, colors,
                          f"{title}\n(Срочные заказы)", ylim)

            # График для отложенных заказов
            delayed_data = df[df[segment_col] == True] if segment_col in df.columns else pd.DataFrame()
            if not delayed_data.empty:
                plot_city_bars(ax2, delayed_data, metric_col, cities, colors,
                              f"{title}\n(Отложенные заказы)", ylim)
            else:
                ax2.text(0.5, 0.5, 'Нет данных\nпо отложенным заказам',
                        ha='center', va='center', transform=ax2.transAxes, fontsize=14)
                ax2.set_title(f"{title}\n(Отложенные заказы)")
                setup_plot_aesthetics(ax2, "", "Города", "Конверсия (%)")

        else:
            # Простой график без сегментации
            fig, ax = plt.subplots(figsize=Config.FIGURE_SIZE)
            plot_city_bars(ax, df, metric_col, cities, colors, title, ylim)

        plt.tight_layout()

        # Сохранение если требуется
        save_plot_if_needed(fig, save_filename)

        plt.show()
        return fig

    except Exception as e:
        logger.error(f"Ошибка при создании графика метрики {metric_col}: {e}")
        return plt.figure()


def plot_city_bars(ax, df: pd.DataFrame, metric_col: str, cities: List[str],
                   colors: List[str], title: str, ylim: tuple):
    """
    Создает столбчатый график по городам для лучшей читаемости.
    """
    try:
        # Агрегируем данные по городам
        city_data = []
        city_labels = []

        for city in cities:
            city_df = df[df['city'] == city] if 'city' in df.columns else df
            if not city_df.empty:
                # Берем среднее значение метрики по городу
                avg_value = city_df[metric_col].mean() * 100
                city_data.append(avg_value)
                city_labels.append(city)

        if not city_data:
            ax.text(0.5, 0.5, 'Нет данных для отображения',
                   ha='center', va='center', transform=ax.transAxes, fontsize=14)
            return

        # Создаем столбчатый график
        x_pos = np.arange(len(city_labels))
        bars = ax.bar(x_pos, city_data, color=colors[:len(city_labels)],
                     alpha=0.8, edgecolor='white', linewidth=2)

        # Добавляем значения на столбцы
        for i, (bar, value) in enumerate(zip(bars, city_data)):
            height = bar.get_height()
            ax.annotate(f'{value:.1f}%',
                       xy=(bar.get_x() + bar.get_width() / 2, height),
                       xytext=(0, 8),  # смещение вверх
                       textcoords="offset points",
                       ha='center', va='bottom',
                       fontsize=11, fontweight='bold',
                       color=colors[i % len(colors)],
                       bbox=dict(boxstyle='round,pad=0.3',
                                facecolor='white',
                                alpha=0.9,
                                edgecolor=colors[i % len(colors)],
                                linewidth=1))

        # Настройка графика
        setup_plot_aesthetics(ax, title, 'Города', 'Конверсия (%)')
        ax.set_xticks(x_pos)
        ax.set_xticklabels(city_labels, rotation=45, ha='right')
        ax.set_ylim(ylim)

        # Добавляем горизонтальные линии для лучшей читаемости
        for y in range(10, int(ylim[1]), 20):
            ax.axhline(y, color='gray', linestyle=':', linewidth=0.6, alpha=0.4)

        # Добавляем среднее значение как референсную линию
        if city_data:
            avg_all = np.mean(city_data)
            ax.axhline(avg_all, color='red', linestyle='--', linewidth=2, alpha=0.7,
                      label=f'Среднее: {avg_all:.1f}%')
            ax.legend(loc='upper right')

    except Exception as e:
        logger.error(f"Ошибка при создании столбчатого графика: {e}")
        ax.text(0.5, 0.5, f'Ошибка: {str(e)}',
               ha='center', va='center', transform=ax.transAxes, fontsize=12)

def create_funnel_chart(df: pd.DataFrame, save_filename: str = None) -> plt.Figure:
    """
    Создает улучшенный график воронки конверсии.

    Args:
        df (pd.DataFrame): DataFrame с агрегированными данными
        save_filename (str, optional): Имя файла для сохранения

    Returns:
        plt.Figure: Объект фигуры matplotlib
    """
    if df.empty:
        logger.warning("Пустой DataFrame для создания воронки")
        return plt.figure()

    set_plot_style()

    try:
        # Агрегируем данные по всем городам и дням
        required_cols = ['cnt_order', 'cnt_offer', 'cnt_assign', 'cnt_arrive', 'cnt_trip']
        available_cols = [col for col in required_cols if col in df.columns]

        if not available_cols:
            logger.error("Отсутствуют необходимые колонки для создания воронки")
            return plt.figure()

        # Суммируем данные
        if 'day_order' in df.columns:
            total_data = df.groupby('day_order')[available_cols].sum().sum()
        else:
            total_data = df[available_cols].sum()

        # Определяем этапы и значения
        stage_mapping = {
            'cnt_order': 'Заявки',
            'cnt_offer': 'Предложения',
            'cnt_assign': 'Назначения',
            'cnt_arrive': 'Прибытия',
            'cnt_trip': 'Поездки'
        }

        stages = []
        values = []

        for col in required_cols:
            if col in available_cols and col in total_data.index:
                stages.append(stage_mapping[col])
                values.append(int(total_data[col]))

        if len(values) < 2:
            logger.warning("Недостаточно данных для создания воронки")
            return plt.figure()

        # Расчет конверсий между этапами
        conversions = []
        conv_labels = []

        for i in range(1, len(values)):
            conv = safe_division(values[i], values[i-1]) * 100
            conversions.append(conv)
            conv_labels.append(f'{stages[i-1]} →\n{stages[i]}')

        # Создаем цветовые палитры
        funnel_colors = create_color_palette(len(stages), 'funnel')
        conversion_colors = create_color_palette(len(conversions), 'conversion')

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 9))

        # График 1: Воронка с количественными показателями
        bars = ax1.bar(stages, values, color=funnel_colors, alpha=0.85,
                       edgecolor='white', linewidth=2.5)

        # Добавляем подписи значений
        add_value_labels(ax1, bars, values, "{:,}")

        # Настройка первого графика
        setup_plot_aesthetics(ax1, 'Воронка конверсии такси', None, 'Количество')
        ax1.set_ylim(0, max(values) * 1.3)
        ax1.tick_params(axis='x', labelsize=11, rotation=15)

        # Добавляем процентные подписи на столбцы (доля от первого этапа)
        if values:
            base_value = values[0]
            for bar, value in zip(bars, values):
                percentage = safe_division(value, base_value) * 100
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height * 0.5,
                        f'{percentage:.1f}%\nот начала',
                        ha='center', va='center',
                        fontweight='bold', fontsize=10,
                        color='white',
                        bbox=dict(boxstyle='round,pad=0.3',
                                 facecolor='black',
                                 alpha=0.7))

        # График 2: Конверсии между этапами
        bars2 = ax2.bar(conv_labels, conversions, color=conversion_colors, alpha=0.85,
                        edgecolor='white', linewidth=2.5)

        # Добавляем подписи процентов
        add_value_labels(ax2, bars2, conversions, "{:.1f}%")

        # Настройка второго графика
        setup_plot_aesthetics(ax2, 'Конверсии между этапами', None, 'Конверсия (%)')
        ax2.set_ylim(0, max(conversions) * 1.25 if conversions else 100)
        ax2.tick_params(axis='x', labelsize=10)

        # Добавляем цветовое кодирование по уровню конверсии
        for bar, conv in zip(bars2, conversions):
            # Меняем цвет в зависимости от уровня конверсии
            if conv >= 80:
                bar.set_color('#27AE60')  # Зеленый - отлично
            elif conv >= 60:
                bar.set_color('#F39C12')  # Оранжевый - хорошо
            elif conv >= 40:
                bar.set_color('#E67E22')  # Темно-оранжевый - требует внимания
            else:
                bar.set_color('#E74C3C')  # Красный - критично

        # Добавляем референсные линии
        for threshold, color, label in [(80, 'green', 'Отлично'),
                                       (60, 'orange', 'Хорошо'),
                                       (40, 'red', 'Критично')]:
            ax2.axhline(threshold, color=color, linestyle='--', alpha=0.5, linewidth=1)
            ax2.text(len(conversions)-0.5, threshold+2, label,
                    color=color, fontsize=9, alpha=0.7)

        plt.tight_layout(pad=4.0)

        # Сохранение если требуется
        save_plot_if_needed(fig, save_filename)

        plt.show()
        return fig

    except Exception as e:
        logger.error(f"Ошибка при создании воронки: {e}")
        return plt.figure()

def create_city_comparison_chart(df: pd.DataFrame, save_filename: str = None) -> plt.Figure:
    """
    Создает улучшенный график сравнения городов по типам заказов.

    Args:
        df (pd.DataFrame): DataFrame с данными
        save_filename (str, optional): Имя файла для сохранения

    Returns:
        plt.Figure: Объект фигуры matplotlib
    """
    if df.empty:
        logger.warning("Пустой DataFrame для сравнения городов")
        return plt.figure()

    set_plot_style()

    # Проверяем наличие необходимых колонок
    required_cols = ['city', 'cnt_order', 'cnt_trip']
    if not validate_dataframe(df, required_cols):
        logger.error("Отсутствуют необходимые колонки для сравнения городов")
        return plt.figure()

    try:
        # Определяем колонку для сегментации
        segment_col = 'is_delayed' if 'is_delayed' in df.columns else None

        if segment_col:
            # Агрегируем данные по городам и типам заказов
            city_data = df.groupby(['city', segment_col]).agg({
                'cnt_order': 'sum',
                'cnt_trip': 'sum'
            }).reset_index()
        else:
            # Простая агрегация по городам
            city_data = df.groupby('city').agg({
                'cnt_order': 'sum',
                'cnt_trip': 'sum'
            }).reset_index()
            city_data[segment_col] = False  # Добавляем фиктивную колонку

        # Рассчитываем конверсию
        city_data['conversion'] = city_data.apply(
            lambda row: safe_division(row['cnt_trip'], row['cnt_order']) * 100, axis=1
        )

        cities = city_data['city'].unique()
        colors = create_color_palette(len(cities), 'city')

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 9))

        # График 1: Количество заказов по городам и типам
        x_pos = np.arange(len(cities))
        width = 0.35

        if segment_col and segment_col in city_data.columns:
            # Разделяем данные по типам заказов
            urgent_orders = []
            delayed_orders = []

            for city in cities:
                urgent = city_data[(city_data['city'] == city) & (city_data[segment_col] == False)]['cnt_order'].sum()
                delayed = city_data[(city_data['city'] == city) & (city_data[segment_col] == True)]['cnt_order'].sum()
                urgent_orders.append(urgent)
                delayed_orders.append(delayed)

            bars1 = ax1.bar(x_pos - width/2, urgent_orders, width,
                            label='Срочные заказы', color='#3498DB', alpha=0.8,
                            edgecolor='white', linewidth=2)
            bars2 = ax1.bar(x_pos + width/2, delayed_orders, width,
                            label='Отложенные заказы', color='#E74C3C', alpha=0.8,
                            edgecolor='white', linewidth=2)

            # Добавляем подписи на столбцы
            add_value_labels(ax1, bars1, urgent_orders, "{:,}")
            add_value_labels(ax1, bars2, delayed_orders, "{:,}")

            title1 = 'Количество заказов по городам и типам'
        else:
            # Простой график по городам
            total_orders = [city_data[city_data['city'] == city]['cnt_order'].sum() for city in cities]
            bars1 = ax1.bar(x_pos, total_orders, color=colors, alpha=0.8,
                           edgecolor='white', linewidth=2)
            add_value_labels(ax1, bars1, total_orders, "{:,}")
            title1 = 'Количество заказов по городам'

        # Настройка первого графика
        setup_plot_aesthetics(ax1, title1, 'Города', 'Количество заказов')
        ax1.set_xticks(x_pos)
        ax1.set_xticklabels(cities, rotation=45, ha='right')

        if segment_col:
            create_enhanced_legend(ax1, 'Тип заказов')

        # График 2: Конверсия по городам и типам
        if segment_col and segment_col in city_data.columns:
            urgent_conv = []
            delayed_conv = []

            for city in cities:
                urgent_data = city_data[(city_data['city'] == city) & (city_data[segment_col] == False)]
                delayed_data = city_data[(city_data['city'] == city) & (city_data[segment_col] == True)]

                urgent_conv.append(urgent_data['conversion'].iloc[0] if len(urgent_data) > 0 else 0)
                delayed_conv.append(delayed_data['conversion'].iloc[0] if len(delayed_data) > 0 else 0)

            bars3 = ax2.bar(x_pos - width/2, urgent_conv, width,
                            label='Срочные заказы', color='#2ECC71', alpha=0.8,
                            edgecolor='white', linewidth=2)
            bars4 = ax2.bar(x_pos + width/2, delayed_conv, width,
                            label='Отложенные заказы', color='#F39C12', alpha=0.8,
                            edgecolor='white', linewidth=2)

            # Добавляем подписи на столбцы
            add_value_labels(ax2, bars3, urgent_conv, "{:.1f}%")
            add_value_labels(ax2, bars4, delayed_conv, "{:.1f}%")

            max_conv = max(max(urgent_conv) if urgent_conv else 0, max(delayed_conv) if delayed_conv else 0)
            title2 = 'Конверсия по городам и типам заказов'
        else:
            # Простой график конверсий по городам
            total_conv = [city_data[city_data['city'] == city]['conversion'].iloc[0]
                         if len(city_data[city_data['city'] == city]) > 0 else 0 for city in cities]

            bars3 = ax2.bar(x_pos, total_conv, color=colors, alpha=0.8,
                           edgecolor='white', linewidth=2)
            add_value_labels(ax2, bars3, total_conv, "{:.1f}%")

            max_conv = max(total_conv) if total_conv else 0
            title2 = 'Конверсия по городам'

        # Настройка второго графика
        setup_plot_aesthetics(ax2, title2, 'Города', 'Конверсия (%)')
        ax2.set_xticks(x_pos)
        ax2.set_xticklabels(cities, rotation=45, ha='right')
        ax2.set_ylim(0, max_conv * 1.2 if max_conv > 0 else 100)

        if segment_col:
            create_enhanced_legend(ax2, 'Тип заказов')

        # Добавляем референсные линии для конверсии
        for threshold, color, label in [(80, 'green', 'Отлично'),
                                       (60, 'orange', 'Хорошо'),
                                       (40, 'red', 'Требует внимания')]:
            if threshold <= max_conv * 1.2:
                ax2.axhline(threshold, color=color, linestyle='--', alpha=0.5, linewidth=1)

        plt.tight_layout(pad=4.0)

        # Сохранение если требуется
        save_plot_if_needed(fig, save_filename)

        plt.show()
        return fig

    except Exception as e:
        logger.error(f"Ошибка при создании сравнения городов: {e}")
        return plt.figure()

def plot_all_metrics(
    df: pd.DataFrame,
    cities: List[str] = None,
    segment_col: Optional[str] = None,
    save_plots: bool = False,
    plot_prefix: str = "taxi_analysis"
) -> Dict[str, plt.Figure]:
    """
    Создает полный набор графиков для анализа данных такси.

    Args:
        df (pd.DataFrame): DataFrame с агрегированными данными
        cities (List[str], optional): Список городов для анализа
        segment_col (str, optional): Колонка для сегментации
        save_plots (bool): Сохранять ли графики в файлы
        plot_prefix (str): Префикс для имен файлов

    Returns:
        Dict[str, plt.Figure]: Словарь с созданными графиками
    """
    if df.empty:
        logger.warning("Пустой DataFrame для создания графиков")
        return {}

    figures = {}

    try:
        logger.info("Создание графиков анализа...")

        # 1. Воронка конверсии
        logger.info("Создание воронки конверсии...")
        funnel_filename = f"{plot_prefix}_funnel.png" if save_plots else None
        figures['funnel'] = create_funnel_chart(df, funnel_filename)

        # 2. Сравнение городов
        logger.info("Создание сравнения городов...")
        city_filename = f"{plot_prefix}_cities.png" if save_plots else None
        figures['cities'] = create_city_comparison_chart(df, city_filename)

        # 3. Детальные метрики конверсии
        logger.info("Создание детальных метрик...")
        metrics = {
            'order2trip': {
                'title': '🎯 Order2Trip - Общая конверсия заказов в поездки',
                'description': 'Ключевая метрика эффективности сервиса'
            },
            'order2offer': {
                'title': '📞 Order2Offer - Конверсия заказов в предложения водителям',
                'description': 'Показывает эффективность системы поиска водителей'
            },
            'offer2assign': {
                'title': '✅ Offer2Assign - Конверсия предложений в назначения',
                'description': 'Отражает готовность водителей принимать заказы'
            },
            'assign2arrive': {
                'title': '🚗 Assign2Arrive - Конверсия назначений в прибытия',
                'description': 'Показывает надежность водителей'
            },
            'arrive2trip': {
                'title': '🏁 Arrive2Trip - Конверсия прибытий в поездки',
                'description': 'Финальный этап - начало поездки'
            }
        }

        for metric, info in metrics.items():
            if metric in df.columns:
                logger.info(f"Создание графика для метрики: {metric}")
                metric_filename = f"{plot_prefix}_{metric}.png" if save_plots else None

                figures[metric] = plot_metric(
                    df,
                    metric,
                    info['title'],
                    ylim=(0, 100),
                    cities=cities,
                    segment_col=segment_col,
                    save_filename=metric_filename
                )
            else:
                logger.warning(f"Метрика {metric} не найдена в данных")

        logger.info(f"Создано {len(figures)} графиков")
        return figures

    except Exception as e:
        logger.error(f"Ошибка при создании графиков: {e}")
        return figures

# ============================================================================
# ГЛАВНАЯ ФУНКЦИЯ И ТОЧКА ВХОДА
# ============================================================================

def main(
    filepath: str = 'taxi_data.xlsx',
    delay_threshold: int = 60,
    outlier_threshold: float = 2.5,
    save_plots: bool = False,
    generate_report: bool = True
) -> None:
    """
    Главная функция для выполнения полного анализа данных такси.

    Args:
        filepath (str): Путь к файлу с данными
        delay_threshold (int): Порог для определения отложенных заказов (минуты)
        outlier_threshold (float): Порог для определения выбросов
        save_plots (bool): Сохранять ли графики в файлы
        generate_report (bool): Генерировать ли текстовый отчет
    """
    print("🚀 ЗАПУСК АНАЛИЗА ДАННЫХ ТАКСИ")
    print("=" * 50)

    try:
        # 1. Загрузка и подготовка данных
        print("\n📂 Загрузка данных...")
        df = load_and_prepare_data(filepath)
        if df.empty:
            print("❌ Не удалось загрузить данные. Анализ прерван.")
            return

        print(f"✅ Данные загружены: {len(df)} записей")

        # 2. Анализ отложенных заказов
        print(f"\n⏰ Анализ отложенных заказов (порог: {delay_threshold} мин)...")
        df_with_delays = mark_delayed_orders(df, threshold_min=delay_threshold)

        # 3. Агрегация метрик
        print("\n📊 Агрегация метрик...")
        agg_data = aggregate_metrics(df_with_delays, segment_col='is_delayed')

        if agg_data.empty:
            print("❌ Не удалось агрегировать данные. Анализ прерван.")
            return

        # 4. Система алертов
        print(f"\n⚠️ Проверка аномалий (порог: {outlier_threshold})...")
        alert_system = alert_outliers(
            agg_data,
            ['order2trip', 'order2offer', 'offer2assign', 'assign2arrive', 'arrive2trip'],
            threshold=outlier_threshold
        )

        # 5. Генерация отчета
        if generate_report:
            print("\n📋 Генерация отчета...")
            report_gen = ReportGenerator()

            # Анализируем данные для отчета
            report_gen.analyze_general_stats(df_with_delays)
            report_gen.analyze_funnel(agg_data)
            report_gen.analyze_cities(agg_data)

            # Выводим отчет
            print("\n" + "="*60)
            print(report_gen.generate_full_report())
            print("="*60)

        # 6. Создание графиков
        print("\n📈 Создание графиков...")
        figures = plot_all_metrics(
            agg_data,
            segment_col='is_delayed',
            save_plots=save_plots,
            plot_prefix="taxi_analysis"
        )

        print(f"✅ Создано {len(figures)} графиков")

        # 7. Финальная сводка
        print("\n🎉 АНАЛИЗ ЗАВЕРШЕН УСПЕШНО!")
        print(f"📊 Проанализировано заказов: {len(df):,}")
        print(f"🏙️ Городов в анализе: {df['city'].nunique() if 'city' in df.columns else 0}")
        print(f"📈 Создано графиков: {len(figures)}")

        if save_plots:
            print("💾 Графики сохранены в текущую директорию")

        if generate_report and 'report_gen' in locals():
            insights_count = len(report_gen.key_insights)
            recommendations_count = len(report_gen.recommendations)
            print(f"💡 Ключевых выводов: {insights_count}")
            print(f"🎯 Рекомендаций: {recommendations_count}")

    except Exception as e:
        logger.error(f"Критическая ошибка в main(): {e}")
        print(f"❌ Критическая ошибка: {e}")
        print("🔧 Проверьте логи для получения детальной информации")

if __name__ == "__main__":
    main()